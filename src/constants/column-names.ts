export const MEMBERSHIP_MODULE_COLUMN_NAMES = {
  MEMBERSHIP: {
    ID: "id",
    TENANT_ID: "tenant_id",
    NAME: "name",
    IS_CUSTOM: "is_custom",
    IS_TRIAL: "is_trial",
    STATUS: "status",
    FEATURES: "features",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    DESCRIPTION: "description",
    CREATED_BY: "created_by",
    UPDATED_BY: "updated_by",
  },
  MEMBERSHIP_CYCLE: {
    ID: "id",
    TENANT_ID: "tenant_id",
    MEMBERSHIP_ID: "membership_id",
    BILLING_CYCLE: "billing_cycle",
    PRICE: "price",
    FEATURES: "features",
    TRIAL_PERIOD_DAYS: "trial_period_days",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  TENANT_MEMBERSHIP: {
    ID: "id",
    TENANT_ID: "tenant_id",
    MEMBERSHIP_ID: "membership_id",
    MEMBERSHIP_CYCLE_ID: "membership_cycle_id",
    STATUS: "status",
    TRIAL_END: "trial_end",
    CREATED_AT: "created_at",
    CREATED_BY: "created_by",
    UPDATED_AT: "updated_at",
    UPDATED_BY: "updated_by",
  },
  BILLING_CYCLE: {
    ID: "id",
    TENANT_MEMBERSHIP_ID: "tenant_membership_id",
    PERIOD_START: "period_start",
    PERIOD_END: "period_end",
    STATUS: "status",
  },
  CAPABILITY: {
    ID: "id",
    KEY: "key",
    DESCRIPTION: "description",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  MEMBERSHIP_CAPABILITY: {
    ID: "id",
    MEMBERSHIP_ID: "membership_id",
    CAPABILITY_ID: "capability_id",
    VALUE: "value",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  TENANT_MEMBERSHIP_EXTERNAL_MAPPING: {
    ID: "id",
    TENANT_MEMBERSHIP_ID: "tenant_membership_id",
    EXTERNAL_TENANT_MEMBERSHIP_ID: "external_tenant_membership_id",
    EXTERNAL_CUSTOMER_ID: "external_customer_id",
    PROVIDER: "provider",
    CREATED_AT: "created_at",
    CREATED_BY: "created_by",
    UPDATED_AT: "updated_at",
    UPDATED_BY: "updated_by",
  },
  MEMBERSHIP_EXTERNAL_MAPPING: {
    ID: "id",
    MEMBERSHIP_ID: "membership_id",
    EXTERNAL_MEMBERSHIP_ID: "external_membership_id",
    PROVIDER: "provider",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  MEMBERSHIP_CYCLE_EXTERNAL_MAPPING: {
    ID: "id",
    MEMBERSHIP_CYCLE_ID: "membership_cycle_id",
    EXTERNAL_CYCLE_ID: "external_cycle_id",
    PROVIDER: "provider",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
};
