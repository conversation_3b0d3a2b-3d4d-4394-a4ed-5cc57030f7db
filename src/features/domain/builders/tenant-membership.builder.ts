import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Provider } from "@heronjs/common";
import { ITenantMembership, TenantMembership } from "@features/domain/aggregates";

export type TenantMembershipBuilderBuildPayload = AggregateRootBuilderPayload<ITenantMembership>;
export type ITenantMembershipBuilder = IAggregateRootBuilder<ITenantMembership>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.BUILDER.TENANT_MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipBuilder
  extends AggregateRootBuilder<ITenantMembership>
  implements ITenantMembershipBuilder
{
  async build(payload?: TenantMembershipBuilderBuildPayload): Promise<ITenantMembership> {
    return new TenantMembership(payload || {});
  }
}
