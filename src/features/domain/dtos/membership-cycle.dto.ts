import { Nullable } from "@heronjs/common";
import { MembershipCycleEnum } from "@features/domain/aggregates";
import { MembershipCycleExternalMappingDto } from "@features/domain/dtos/membership-cycle-external-mapping.dto";

export type MembershipCycleDto = {
  id: string;
  membershipId: string;
  billingCycle: MembershipCycleEnum;
  amount: number;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  externalMappings?: MembershipCycleExternalMappingDto[];
};
