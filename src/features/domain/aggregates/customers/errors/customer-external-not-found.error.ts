import { RuntimeError } from "@heronjs/common";
import { CustomerExternalMapping } from "@features/domain/aggregates/customers/entities/customer-external-mapping";

export enum CustomerExternalMappingErrorCodes {
  NOT_FOUND = "NOT_FOUND",
}

export class CustomerExternalNotFoundError extends RuntimeError {
  constructor() {
    super(
      CustomerExternalMapping.ENTITY_NAME,
      CustomerExternalMappingErrorCodes.NOT_FOUND,
      "Customer external mapping not found",
    );
  }
}
