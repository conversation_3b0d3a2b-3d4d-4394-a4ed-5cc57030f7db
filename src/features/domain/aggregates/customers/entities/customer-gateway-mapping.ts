import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateCustomerGatewayMappingInput = {
  customerId: string; // This refers to the user_id in tbl_users from the user module
  externalCustomerId: string;
  provider: string;
  createdBy: string;
};

export type UpdateCustomerGatewayMappingInput = {
  id: string;
  externalCustomerId?: string;
  provider?: string;
  updatedBy?: string;
};

export type CustomerGatewayMappingProps = {
  customerId: string; // This refers to the user_id in tbl_users from the user module
  externalCustomerId: string;
  provider: string;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
};

export type CustomerGatewayMappingMethods = {
  create(input: CreateCustomerGatewayMappingInput): Promise<void>;
  update(input: UpdateCustomerGatewayMappingInput): Promise<void>;
};

export type ICustomerGatewayMapping = IEntity<
  CustomerGatewayMappingProps,
  CustomerGatewayMappingMethods
>;

export class CustomerGatewayMapping
  extends Entity<CustomerGatewayMappingProps, CustomerGatewayMappingMethods>
  implements ICustomerGatewayMapping
{
  static readonly ENTITY_NAME = "CustomerGatewayMapping";

  constructor(props?: EntityConstructorPayload<CustomerGatewayMappingProps>) {
    super(props);
  }

  /** Props **/
  get customerId(): string {
    return this.props.customerId;
  }

  get externalCustomerId(): string {
    return this.props.externalCustomerId;
  }

  get provider(): string {
    return this.props.provider;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  get updatedBy(): Nullable<string> {
    return this.props.updatedBy;
  }

  /** Methods **/
  private setCustomerId(payload?: string) {
    if (payload !== undefined) this.setProp("customerId", payload);
  }

  private setExternalCustomerId(payload?: string) {
    if (payload !== undefined) this.setProp("externalCustomerId", payload);
  }

  private setProvider(payload?: string) {
    if (payload !== undefined) this.setProp("provider", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setCreatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("createdBy", payload);
  }

  private setUpdatedAt(payload?: Nullable<Date>) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  private setUpdatedBy(payload?: Nullable<string>) {
    if (payload !== undefined) this.setProp("updatedBy", payload);
  }

  async create(payload: CreateCustomerGatewayMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setCustomerId(payload.customerId);
    this.setExternalCustomerId(payload.externalCustomerId);
    this.setProvider(payload.provider);
    this.setCreatedAt(new Date());
    this.setCreatedBy(payload.createdBy);
  }

  async update(payload: UpdateCustomerGatewayMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setExternalCustomerId(payload.externalCustomerId);
    this.setProvider(payload.provider);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(payload.updatedBy);
  }
}
