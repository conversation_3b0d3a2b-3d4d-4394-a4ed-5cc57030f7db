import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateCustomerExternalMappingInput = {
  customerId: string; // This refers to the user_id in tbl_users from the user module
  externalCustomerId: string;
  provider: string;
  createdBy: string;
};

export type UpdateCustomerExternalMappingInput = {
  id: string;
  externalCustomerId?: string;
  provider?: string;
  updatedBy?: string;
};

export type CustomerExternalMappingProps = {
  customerId: string; // This refers to the user_id in tbl_users from the user module
  externalCustomerId: string;
  provider: string;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
};

export type CustomerExternalMappingMethods = {
  create(input: CreateCustomerExternalMappingInput): Promise<void>;
  update(input: UpdateCustomerExternalMappingInput): Promise<void>;
};

export type ICustomerExternalMapping = IEntity<
  CustomerExternalMappingProps,
  CustomerExternalMappingMethods
>;

export class CustomerExternalMapping
  extends Entity<CustomerExternalMappingProps, CustomerExternalMappingMethods>
  implements ICustomerExternalMapping
{
  static readonly ENTITY_NAME = "CustomerExternalMapping";

  constructor(props?: EntityConstructorPayload<CustomerExternalMappingProps>) {
    super(props);
  }

  /** Props **/
  get customerId(): string {
    return this.props.customerId;
  }

  get externalCustomerId(): string {
    return this.props.externalCustomerId;
  }

  get provider(): string {
    return this.props.provider;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  get updatedBy(): Nullable<string> {
    return this.props.updatedBy;
  }

  /** Methods **/
  private setCustomerId(payload?: string) {
    if (payload !== undefined) this.setProp("customerId", payload);
  }

  private setExternalCustomerId(payload?: string) {
    if (payload !== undefined) this.setProp("externalCustomerId", payload);
  }

  private setProvider(payload?: string) {
    if (payload !== undefined) this.setProp("provider", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setCreatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("createdBy", payload);
  }

  private setUpdatedAt(payload?: Nullable<Date>) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  private setUpdatedBy(payload?: Nullable<string>) {
    if (payload !== undefined) this.setProp("updatedBy", payload);
  }

  async create(payload: CreateCustomerExternalMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setCustomerId(payload.customerId);
    this.setExternalCustomerId(payload.externalCustomerId);
    this.setProvider(payload.provider);
    this.setCreatedAt(new Date());
    this.setCreatedBy(payload.createdBy);
  }

  async update(payload: UpdateCustomerExternalMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setExternalCustomerId(payload.externalCustomerId);
    this.setProvider(payload.provider);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(payload.updatedBy);
  }
}
