import { RuntimeError } from "@heronjs/common";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";

export class MembershipExternalNotFoundError extends RuntimeError {
  constructor() {
    super(
      Membership.AGGREGATE_NAME,
      MembershipErrorCodes.EXTERNAL_NOT_FOUND,
      "Membership external mapping not found",
    );
  }
}
