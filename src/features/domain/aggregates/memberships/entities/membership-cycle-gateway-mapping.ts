import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";
import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";

export type CreateMembershipCycleGatewayMappingInput = {
  membershipCycleId: string;
  gatewayMembershipCycleId: string;
  gateway: string;
};

export type UpdateMembershipCycleGatewayMappingInput = {
  id: string;
  gatewayMembershipCycleId?: string;
  gateway?: string;
};

export type MembershipCycleGatewayMappingProps = {
  membershipCycleId: string;
  gatewayMembershipCycleId: string;
  gateway: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type MembershipCycleGatewayMappingMethods = {
  create(input: CreateMembershipCycleGatewayMappingInput): Promise<void>;
  update(input: UpdateMembershipCycleGatewayMappingInput): Promise<void>;
};

export type IMembershipCycleGatewayMapping = IEntity<
  MembershipCycleGatewayMappingProps,
  MembershipCycleGatewayMappingMethods
>;

export class MembershipCycleGatewayMapping
  extends Entity<MembershipCycleGatewayMappingProps, MembershipCycleGatewayMappingMethods>
  implements IMembershipCycleGatewayMapping
{
  static readonly ENTITY_NAME = "MembershipCycleGatewayMapping";

  constructor(props?: EntityConstructorPayload<MembershipCycleGatewayMappingProps>) {
    super(props);
  }

  /** Props **/
  get membershipCycleId(): string {
    return this.props.membershipCycleId;
  }

  get gatewayMembershipCycleId(): string {
    return this.props.gatewayMembershipCycleId;
  }

  get gateway(): string {
    return this.props.gateway;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setMembershipCycleId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipCycleId", payload);
  }

  private setGatewayMembershipCycleId(payload?: string) {
    if (payload !== undefined) this.setProp("gatewayMembershipCycleId", payload);
  }

  private setGateway(payload?: string) {
    if (payload !== undefined) this.setProp("gateway", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateMembershipCycleGatewayMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setGatewayMembershipCycleId(payload.gatewayMembershipCycleId);
    this.setGateway(payload.gateway);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateMembershipCycleGatewayMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setGatewayMembershipCycleId(payload.gatewayMembershipCycleId);
    this.setGateway(payload.gateway);
    this.setUpdatedAt(new Date());
  }
}
