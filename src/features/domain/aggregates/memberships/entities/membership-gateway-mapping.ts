import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";
import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";

export type CreateMembershipGatewayMappingInput = {
  membershipId: string;
  gatewayMembershipId: string;
  gateway: string;
};

export type UpdateMembershipGatewayMappingInput = {
  id: string;
  gatewayMembershipId?: string;
  gateway?: string;
  updatedBy?: string;
};

export type MembershipGatewayMappingProps = {
  membershipId: string;
  gatewayMembershipId: string;
  gateway: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type MembershipGatewayMappingMethods = {
  create(input: CreateMembershipGatewayMappingInput): Promise<void>;
  update(input: UpdateMembershipGatewayMappingInput): Promise<void>;
};

export type IMembershipGatewayMapping = IEntity<
  MembershipGatewayMappingProps,
  MembershipGatewayMappingMethods
>;

export class MembershipGatewayMapping
  extends Entity<MembershipGatewayMappingProps, MembershipGatewayMappingMethods>
  implements IMembershipGatewayMapping
{
  static readonly ENTITY_NAME = "MembershipGatewayMapping";

  constructor(props?: EntityConstructorPayload<MembershipGatewayMappingProps>) {
    super(props);
  }

  /** Props **/
  get membershipId(): string {
    return this.props.membershipId;
  }

  get gatewayMembershipId(): string {
    return this.props.gatewayMembershipId;
  }

  get gateway(): string {
    return this.props.gateway;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipId", payload);
  }

  private setGatewayMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("gatewayMembershipId", payload);
  }

  private setGateway(payload?: string) {
    if (payload !== undefined) this.setProp("gateway", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateMembershipGatewayMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setMembershipId(payload.membershipId);
    this.setGatewayMembershipId(payload.gatewayMembershipId);
    this.setGateway(payload.gateway);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateMembershipGatewayMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setGatewayMembershipId(payload.gatewayMembershipId);
    this.setGateway(payload.gateway);
    this.setUpdatedAt(new Date());
  }
}
