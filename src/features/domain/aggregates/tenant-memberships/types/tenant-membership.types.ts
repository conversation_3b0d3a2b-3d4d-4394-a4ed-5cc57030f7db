import { PaymentGatewayProvider } from "@features/domain/services";

export type CreateTenantMembershipInput = {
  tenantId: string;
  membershipId: string;
  membershipCycleId: string;
  paymentMethodId?: string;
  gateway?: PaymentGatewayProvider;
  isTrial?: boolean;
  trialPeriodDays?: number;
  createdBy: string;
};

export type UpdateTenantMembershipInput = {
  id: string;
  isActive?: boolean;
  updatedBy?: string;
} & Partial<Omit<CreateTenantMembershipInput, "createdBy">>;
