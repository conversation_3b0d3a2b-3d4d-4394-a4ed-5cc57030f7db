import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { TimestampUtil } from "@shared";
import { Nullable } from "@heronjs/common";
import { BillingCycleStatusEnum } from "@features/domain/aggregates/tenant-memberships/enums";

export type CreateBillingCycleInput = {
  tenantMembershipId: string;
  paymentMethodId?: string;
  periodStart?: Nullable<number>;
  periodEnd?: Nullable<number>;
  invoiceGateway: string;
};

export type UpdateBillingCycleInput = {
  status?: BillingCycleStatusEnum;
} & Partial<CreateBillingCycleInput>;

export type BillingCycleProps = {
  tenantMembershipId: string;
  paymentMethodId: Nullable<string>;
  periodStart: Nullable<number>;
  periodEnd: Nullable<number>;
  status: BillingCycleStatusEnum;
  invoiceGateway: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type BillingCycleMethods = {
  create(input: CreateBillingCycleInput): Promise<void>;
  update(input: UpdateBillingCycleInput): Promise<void>;
};

export type IBillingCycle = IEntity<BillingCycleProps, BillingCycleMethods>;
export class BillingCycle
  extends Entity<BillingCycleProps, BillingCycleMethods>
  implements IBillingCycle
{
  constructor(props?: EntityConstructorPayload<BillingCycleProps>) {
    super(props);
  }

  /** Props **/

  get tenantMembershipId(): string {
    return this.props.tenantMembershipId;
  }

  get paymentMethodId(): Nullable<string> {
    return this.props.paymentMethodId;
  }

  get periodStart(): Nullable<number> {
    return this.props.periodStart;
  }

  get periodEnd(): Nullable<number> {
    return this.props.periodEnd;
  }

  get status(): BillingCycleStatusEnum {
    return this.props.status;
  }

  get invoiceGateway(): string {
    return this.props.invoiceGateway;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setTenantMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("tenantMembershipId", payload);
  }

  private setPaymentMethodId(payload?: Nullable<string>) {
    if (payload !== undefined) this.setProp("paymentMethodId", payload);
  }

  private setPeriodStart(payload?: Nullable<number>) {
    const value = TimestampUtil.convertSecToMs(payload ?? Date.now());
    if (payload !== undefined) this.setProp("periodStart", value);
  }

  private setPeriodEnd(payload?: Nullable<number>) {
    const value = TimestampUtil.convertSecToMs(payload ?? Date.now());
    if (payload !== undefined) this.setProp("periodEnd", value);
  }

  private setStatus(payload?: BillingCycleStatusEnum) {
    if (payload !== undefined) this.setProp("status", payload);
  }

  private setInvoiceGateway(payload?: string) {
    if (payload !== undefined) this.setProp("invoiceGateway", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateBillingCycleInput): Promise<void> {
    this.setId(randomUUID());
    this.setTenantMembershipId(payload.tenantMembershipId);
    this.setPeriodStart(payload.periodStart);
    this.setPeriodEnd(payload.periodEnd);
    this.setPaymentMethodId(payload.paymentMethodId);
    this.setInvoiceGateway(payload.invoiceGateway);
    this.setStatus(BillingCycleStatusEnum.PENDING);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateBillingCycleInput): Promise<void> {
    this.setStatus(payload.status);
    this.setPeriodStart(payload.periodStart);
    this.setPeriodEnd(payload.periodEnd);
    this.setUpdatedAt(new Date());
  }
}
