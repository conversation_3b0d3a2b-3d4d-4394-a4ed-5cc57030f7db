import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateTenantMembershipExternalMappingInput = {
  provider: string;
  tenantMembershipId: string;
  externalCustomerId: string;
  externalTenantMembershipId: string;
};

export type UpdateTenantMembershipExternalMappingInput = {
  id: string;
  provider?: string;
  externalCustomerId?: string;
  externalTenantMembershipId?: string;
};

export type TenantMembershipExternalMappingProps = {
  provider: string;
  tenantMembershipId: string;
  externalCustomerId: string;
  externalTenantMembershipId: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type TenantMembershipExternalMappingMethods = {
  create(input: CreateTenantMembershipExternalMappingInput): Promise<void>;
  update(input: UpdateTenantMembershipExternalMappingInput): Promise<void>;
};

export type ITenantMembershipExternalMapping = IEntity<
  TenantMembershipExternalMappingProps,
  TenantMembershipExternalMappingMethods
>;

export class TenantMembershipExternalMapping
  extends Entity<TenantMembershipExternalMappingProps, TenantMembershipExternalMappingMethods>
  implements ITenantMembershipExternalMapping
{
  constructor(props?: EntityConstructorPayload<TenantMembershipExternalMappingProps>) {
    super(props);
  }

  /** Props **/
  get tenantMembershipId(): string {
    return this.props.tenantMembershipId;
  }

  get provider(): string {
    return this.props.provider;
  }

  get externalTenantMembershipId(): string {
    return this.props.externalTenantMembershipId;
  }

  get externalCustomerId(): string {
    return this.props.externalCustomerId;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setTenantMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("tenantMembershipId", payload);
  }

  private setProvider(payload?: string) {
    if (payload !== undefined) this.setProp("provider", payload);
  }

  private setExternalTenantMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("externalTenantMembershipId", payload);
  }

  private setExternalCustomerId(payload?: string) {
    if (payload !== undefined) this.setProp("externalCustomerId", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateTenantMembershipExternalMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setProvider(payload.provider);
    this.setTenantMembershipId(payload.tenantMembershipId);
    this.setExternalCustomerId(payload.externalCustomerId);
    this.setExternalTenantMembershipId(payload.externalTenantMembershipId);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateTenantMembershipExternalMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setProvider(payload.provider);
    this.setExternalCustomerId(payload.externalCustomerId);
    this.setExternalTenantMembershipId(payload.externalTenantMembershipId);
    this.setUpdatedAt(new Date());
  }
}
