import { randomUUID } from "crypto";
import { Nullable, Optional } from "@heronjs/common";
import {
  AggregateRoot,
  IAggregateRoot,
  AggregateRootConstructorPayload,
} from "@cbidigital/aqua-ddd";
import { CreateTenantMembershipInput, UpdateTenantMembershipInput } from "./types";
import {
  BillingCycle,
  IBillingCycle,
  CreateBillingCycleInput,
  ITenantMembershipExternalMapping,
} from "./entities";
import {
  BillingCycleStatusEnum,
  TenantMembershipStatusEnum,
} from "@features/domain/aggregates/tenant-memberships/enums";
import { PaymentGatewayProvider } from "@features/domain/services";

export type TenantMembershipProps = {
  tenantId: string;
  membershipId: string;
  membershipCycleId: string;
  paymentMethodId: Nullable<string>;
  status: TenantMembershipStatusEnum;
  trialEnd: Nullable<number>;
  gateway: Nullable<PaymentGatewayProvider>;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  billingCycles: IBillingCycle[];
  externalTenantMembershipMappings?: ITenantMembershipExternalMapping[];
};

export type TenantMembershipMethods = {
  create(payload: CreateTenantMembershipInput): Promise<void>;
  update(payload: UpdateTenantMembershipInput): Promise<void>;
  cancel(): Promise<void>;
  expire(): Promise<void>;
  activate(): Promise<void>;
  deactivate(): Promise<void>;
  addBillingCycle(
    payload: Omit<CreateBillingCycleInput, "tenantMembershipId">,
  ): Promise<IBillingCycle>;
  getBillingCycles(): IBillingCycle[];
  getPendingBillingCycle(): Nullable<IBillingCycle>;
  extendTrialPeriod(days: number): Promise<void>;
  onInvoiceCreated(): Promise<void>;
  onPaymentSucceeded(): Promise<void>;
  onPaymentFailed(): Promise<void>;
  onPaymentCancelled(): Promise<void>;
  getTenantMembershipExternalMapping(provider: string): Nullable<ITenantMembershipExternalMapping>;
};

export type ITenantMembership = IAggregateRoot<TenantMembershipProps, TenantMembershipMethods>;

export class TenantMembership
  extends AggregateRoot<TenantMembershipProps, TenantMembershipMethods>
  implements ITenantMembership
{
  static AGGREGATE_NAME = "tenant-membership";

  constructor(payload: AggregateRootConstructorPayload<TenantMembershipProps>) {
    super(payload);
  }

  /** Props **/
  get tenantId(): string {
    return this.props.tenantId;
  }

  get membershipId(): string {
    return this.props.membershipId;
  }

  get membershipCycleId(): string {
    return this.props.membershipCycleId;
  }

  get paymentMethodId(): Nullable<string> {
    return this.props.paymentMethodId;
  }

  get status(): TenantMembershipStatusEnum {
    return this.props.status;
  }

  get trialEnd(): Nullable<number> {
    return this.props.trialEnd;
  }

  get gateway(): Nullable<PaymentGatewayProvider> {
    return this.props.gateway;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  get updatedBy(): Nullable<string> {
    return this.props.updatedBy;
  }

  get billingCycles(): IBillingCycle[] {
    return this.props.billingCycles;
  }

  get externalTenantMembershipMappings(): Optional<ITenantMembershipExternalMapping[]> {
    return this.props.externalTenantMembershipMappings;
  }

  /** Methods **/
  private setTenantId(payload?: string) {
    if (payload !== undefined) this.setProp("tenantId", payload);
  }

  private setMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipId", payload);
  }

  private setMembershipCycleId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipCycleId", payload);
  }

  private setPaymentMethodId(payload?: string) {
    if (payload !== undefined) this.setProp("paymentMethodId", payload);
  }

  private setStatus(payload?: TenantMembershipStatusEnum) {
    if (payload !== undefined) this.setProp("status", payload);
  }

  private setTrialEnd(payload?: number) {
    if (payload !== undefined) this.setProp("trialEnd", payload);
  }

  private setGateway(payload?: Nullable<PaymentGatewayProvider>) {
    if (payload !== undefined) this.setProp("gateway", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setCreatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("createdBy", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  private setUpdatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("updatedBy", payload);
  }

  async create(payload: CreateTenantMembershipInput): Promise<void> {
    this.setId(randomUUID());
    this.setTenantId(payload.tenantId);
    this.setMembershipId(payload.membershipId);
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setPaymentMethodId(payload.paymentMethodId);
    this.setGateway(payload.gateway);
    this.setCreatedAt(new Date());
    this.setCreatedBy(payload.createdBy);
    this.setProp("billingCycles", []);
    const status = payload.isTrial
      ? TenantMembershipStatusEnum.TRIALING
      : TenantMembershipStatusEnum.PENDING;
    this.setStatus(status);
    if (payload.isTrial && payload.trialPeriodDays) {
      this.setTrialEnd(Date.now() + payload.trialPeriodDays * 24 * 60 * 60 * 1000);
    }
  }

  async update(payload: UpdateTenantMembershipInput): Promise<void> {
    this.setId(payload.id);
    this.setTenantId(payload.tenantId);
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(payload.updatedBy);
  }

  async cancel(): Promise<void> {
    this.setStatus(TenantMembershipStatusEnum.CANCELED);
    this.setUpdatedAt(new Date());
  }

  async expire(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async activate(): Promise<void> {
    this.setStatus(TenantMembershipStatusEnum.ACTIVE);
    this.setUpdatedAt(new Date());
  }

  async deactivate(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async onPaymentFailed(): Promise<void> {
    if (this.status === TenantMembershipStatusEnum.ACTIVE) {
      this.setStatus(TenantMembershipStatusEnum.EXPIRED);
    }
    this.setUpdatedAt(new Date());
  }

  async onPaymentCancelled(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async onInvoiceCreated(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async onPaymentSucceeded(): Promise<void> {
    this.setStatus(TenantMembershipStatusEnum.ACTIVE);
    this.setUpdatedAt(new Date());
  }

  /**
   * Adds a membership change with the provided details
   *
   * @param payload - The membership change input data
   * @returns The created membership change entity
   */
  async addBillingCycle(
    payload: Omit<CreateBillingCycleInput, "tenantMembershipId">,
  ): Promise<IBillingCycle> {
    const billingCycle = new BillingCycle();
    await billingCycle.create({ ...payload, tenantMembershipId: this.id });

    const billingCycles = [...this.billingCycles, billingCycle];
    this.setProp("billingCycles", billingCycles);

    return billingCycle;
  }

  getBillingCycles(): IBillingCycle[] {
    return this.billingCycles;
  }

  getPendingBillingCycle(): Nullable<IBillingCycle> {
    if (!this.billingCycles.length) return null;
    const currentBillingCycle = this.billingCycles.find(
      (billingCycle) => billingCycle.status === BillingCycleStatusEnum.PENDING,
    );

    return currentBillingCycle ?? null;
  }

  getTenantMembershipExternalMapping(provider: string): Nullable<ITenantMembershipExternalMapping> {
    if (!this.externalTenantMembershipMappings?.length) return null;
    return this.externalTenantMembershipMappings.find((m) => m.provider === provider) || null;
  }

  async extendTrialPeriod(days: number): Promise<void> {
    const DAY_IN_MS = 24 * 60 * 60 * 1000;
    const now = Date.now();
    this.setTrialEnd(now + days * DAY_IN_MS);
  }
}
