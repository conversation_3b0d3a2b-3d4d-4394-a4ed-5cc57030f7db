import { RuntimeError } from "@heronjs/common";
import { TenantMembership } from "@features/domain/aggregates/tenant-memberships/tenant-membership";
import { TenantMembershipErrorCodes } from "@features/domain/aggregates/tenant-memberships/errors/errors";

export class ActiveMembershipExistsError extends RuntimeError {
  constructor() {
    super(
      TenantMembership.AGGREGATE_NAME,
      TenantMembershipErrorCodes.ACTIVE_MEMBERSHIP_EXISTS,
      "Please cancel the current membership before assigning a new one.",
    );
  }
}
