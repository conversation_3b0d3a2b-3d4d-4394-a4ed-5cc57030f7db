import { RuntimeError } from "@heronjs/common";
import { TenantMembership } from "@features/domain/aggregates/tenant-memberships/tenant-membership";
import { TenantMembershipErrorCodes } from "@features/domain/aggregates/tenant-memberships/errors/errors";

export class RetrieveInvoiceError extends RuntimeError {
  constructor() {
    super(
      TenantMembership.AGGREGATE_NAME,
      TenantMembershipErrorCodes.RETRIEVE_INVOICE_FAILED,
      "Failed to retrieve invoice.",
    );
  }
}
