import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BillingCycleDto } from "@features/domain/dtos";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { BillingCycle, IBillingCycle } from "@features/domain/aggregates";

export type IBillingCycleMapper = IMapper<BillingCycleDto, IBillingCycle>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.BILLING_CYCLE,
  scope: Lifecycle.Singleton,
})
export class BillingCycleMapper extends BaseMapper implements IBillingCycleMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: IBillingCycle): Promise<BillingCycleDto> {
    return {
      id: entity.id,
      tenantMembershipId: entity.tenantMembershipId,
      paymentMethodId: entity.paymentMethodId,
      periodStart: entity.periodStart,
      periodEnd: entity.periodEnd,
      status: entity.status,
      invoiceGateway: entity.invoiceGateway,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: BillingCycleDto): Promise<IBillingCycle> {
    return new BillingCycle({
      id: dto.id,
      props: {
        tenantMembershipId: dto.tenantMembershipId,
        paymentMethodId: dto.paymentMethodId,
        periodStart: dto.periodStart,
        periodEnd: dto.periodEnd,
        status: dto.status,
        invoiceGateway: dto.invoiceGateway,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
