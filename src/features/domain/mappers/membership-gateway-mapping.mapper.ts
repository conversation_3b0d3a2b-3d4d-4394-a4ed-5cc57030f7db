import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { MembershipGatewayMappingDto } from "@features/domain";
import { MembershipGatewayMapping, IMembershipGatewayMapping } from "@features/domain/aggregates";

export type IMembershipGatewayMappingMapper = IMapper<
  MembershipGatewayMappingDto,
  IMembershipGatewayMapping
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_GATEWAY_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipGatewayMappingMapper
  extends BaseMapper
  implements IMembershipGatewayMappingMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(entity: IMembershipGatewayMapping): Promise<MembershipGatewayMappingDto> {
    return {
      id: entity.id,
      membershipId: entity.membershipId,
      gatewayMembershipId: entity.gatewayMembershipId,
      gateway: entity.gateway,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: MembershipGatewayMappingDto): Promise<IMembershipGatewayMapping> {
    return new MembershipGatewayMapping({
      id: dto.id,
      props: {
        membershipId: dto.membershipId,
        gatewayMembershipId: dto.gatewayMembershipId,
        gateway: dto.gateway,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
