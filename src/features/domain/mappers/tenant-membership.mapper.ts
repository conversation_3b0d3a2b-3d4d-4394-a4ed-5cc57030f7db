import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { TenantMembershipDto } from "@features/domain/dtos";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IBillingCycleMapper } from "@features/domain/mappers/billing-cycle.mapper";
import { ITenantMembershipExternalMappingMapper } from "@features/domain/mappers/tenant-membership-external-mapping.mapper";
import { TenantMembership, ITenantMembership } from "@features/domain/aggregates";

export type ITenantMembershipMapper = IMapper<TenantMembershipDto, ITenantMembership>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipMapper extends BaseMapper implements ITenantMembershipMapper {
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.BILLING_CYCLE)
    protected readonly billingCycleMapper: IBilling<PERSON>ycleMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP_EXTERNAL_MAPPING)
    protected readonly externalMappingMapper: ITenantMembershipExternalMappingMapper,
  ) {
    super();
  }

  async fromEntityToDto(entity: ITenantMembership): Promise<TenantMembershipDto> {
    const billingCycles = await this.billingCycleMapper.fromEntitiesToDtos(entity.billingCycles);

    return {
      id: entity.id,
      tenantId: entity.tenantId,
      membershipId: entity.membershipId,
      paymentMethodId: entity.paymentMethodId,
      membershipCycleId: entity.membershipCycleId,
      gateway: entity.gateway,
      status: entity.status,
      trialEnd: entity.trialEnd,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      updatedAt: entity.updatedAt,
      updatedBy: entity.updatedBy,
      billingCycles,
    };
  }

  async fromDtoToEntity(dto: TenantMembershipDto): Promise<ITenantMembership> {
    // Map membership changes from DTO to entities
    const billingCycles = dto.billingCycles
      ? await this.billingCycleMapper.fromDtosToEntities(dto.billingCycles)
      : [];

    const externalTenantMembershipMappings = await this.externalMappingMapper.fromDtosToEntities(
      dto.externalTenantMembershipMappings ?? [],
    );

    // Create the tenant membership entity with all properties including membership changes
    const tenantMembership = new TenantMembership({
      id: dto.id,
      props: {
        tenantId: dto.tenantId,
        membershipId: dto.membershipId,
        membershipCycleId: dto.membershipCycleId,
        paymentMethodId: dto.paymentMethodId,
        status: dto.status,
        gateway: dto.gateway,
        trialEnd: dto.trialEnd,
        createdAt: dto.createdAt,
        createdBy: dto.createdBy,
        updatedAt: dto.updatedAt,
        updatedBy: dto.updatedBy,
        billingCycles,
        externalTenantMembershipMappings,
      },
    });

    return tenantMembership;
  }
}
