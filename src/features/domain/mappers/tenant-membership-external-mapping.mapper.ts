import {
  TenantMembershipExternalMapping,
  ITenantMembershipExternalMapping,
} from "@features/domain/aggregates";
import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { TenantMembershipExternalMappingDto } from "@features/domain";

export type ITenantMembershipExternalMappingMapper = IMapper<
  TenantMembershipExternalMappingDto,
  ITenantMembershipExternalMapping
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipExternalMappingMapper
  extends BaseMapper
  implements ITenantMembershipExternalMappingMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: ITenantMembershipExternalMapping,
  ): Promise<TenantMembershipExternalMappingDto> {
    return {
      id: entity.id,
      tenantMembershipId: entity.tenantMembershipId,
      externalTenantMembershipId: entity.externalTenantMembershipId,
      externalCustomerId: entity.externalCustomerId,
      provider: entity.provider,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(
    dto: TenantMembershipExternalMappingDto,
  ): Promise<ITenantMembershipExternalMapping> {
    return new TenantMembershipExternalMapping({
      id: dto.id,
      props: {
        tenantMembershipId: dto.tenantMembershipId,
        externalTenantMembershipId: dto.externalTenantMembershipId,
        externalCustomerId: dto.externalCustomerId,
        provider: dto.provider,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
