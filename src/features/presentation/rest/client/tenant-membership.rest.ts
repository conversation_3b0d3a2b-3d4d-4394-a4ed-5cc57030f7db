import { Get, Rest, Body, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>m, Principal } from "@heronjs/common";
import {
  IGetCurrentTenantMembershipUseCase,
  ICancelTenantMembershipUseCase,
  IActivateTenantMembershipUseCase,
  ActivateTenantMembershipUseCaseInput,
} from "@features/app";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";

@Rest("/tenant-memberships")
export class TenantMembershipRest {
  @Get({ uri: "/current" })
  @Guard({ private: true })
  async getCurrent(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.GET_CURRENT_TENANT_MEMBERSHIP)
    useCase: IGetCurrentTenantMembershipUseCase,
    @Principal("sub") authId: string,
    @Principal("organization") tenantId: string,
  ) {
    const result = await useCase.exec({ tenantId }, { auth: { authId } });
    return result;
  }

  @Post({ uri: "/activate", code: 201 })
  @Guard({ private: true })
  async activate(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.ACTIVATE_TENANT_MEMBERSHIP)
    useCase: IActivateTenantMembershipUseCase,
    @Principal("sub") authId: string,
    @Principal("organization") tenantId: string,
    @Body() body: ActivateTenantMembershipUseCaseInput,
  ) {
    const result = await useCase.exec(body, { auth: { authId }, tenantId });
    return result;
  }

  @Post({ uri: "/:id/cancel", code: 201 })
  @Guard({ private: true })
  async cancel(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.CANCEL_TENANT_MEMBERSHIP)
    useCase: ICancelTenantMembershipUseCase,
    @Principal("sub") authId: string,
    @Param("id") tenantMembershipId: string,
  ) {
    const result = await useCase.exec({ tenantMembershipId }, { auth: { authId } });
    return result;
  }
}
