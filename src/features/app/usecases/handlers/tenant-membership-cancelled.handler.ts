import {
  IDatabaseUtil,
  ITenantMembershipRepository,
  TenantMembershipNotFoundError,
  MissingTenantMembershipIdError,
  TenantMembershipCancelledEvent,
} from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHandler } from "@features/app/usecases/handlers/webhook-handler.factory";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.HANDLER.TENANT_MEMBERSHIP_CANCELLED,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipCancelledHandler implements IWebhookHandler {
  private readonly logger: ILogger;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: TenantMembershipCancelledEvent) {
    const { data } = event;
    const { subscriptionId } = data;
    if (!subscriptionId) throw new MissingTenantMembershipIdError();
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const externalMapping = await this.tenantMembershipRepo.findOneExternalMapping(
        { filter: { externalTenantMembershipId: { $eq: subscriptionId } } },
        repoOptions,
      );
      if (!externalMapping) throw new TenantMembershipNotFoundError();
      const tenantMembershipId = externalMapping.tenantMembershipId;
      const tenantMembership = await this.tenantMembershipRepo.findOne(
        { filter: { id: { $eq: tenantMembershipId } } },
        repoOptions,
      );
      if (!tenantMembership) throw new TenantMembershipNotFoundError();
      await tenantMembership.cancel();
      await this.tenantMembershipRepo.update(tenantMembership, repoOptions);
      await trx.rollback();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle tenant membership cancelled", error);
      throw error;
    }
  }
}
