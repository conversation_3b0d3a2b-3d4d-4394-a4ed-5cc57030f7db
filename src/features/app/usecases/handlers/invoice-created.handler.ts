import {
  IDatabaseUtil,
  InvoiceCreatedEvent,
  IMembershipRepository,
  IPaymentGatewayFactory,
  ITenantMembershipRepository,
  TenantMembershipNotFoundError,
  MissingTenantMembershipIdError,
} from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhook<PERSON>andler } from "@features/app/usecases/handlers/webhook-handler.factory";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.HANDLER.INVOICE_CREATED,
  scope: Lifecycle.Singleton,
})
export class InvoiceCreatedHandler implements IWebhookHandler {
  private readonly logger: ILogger;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: InvoiceCreatedEvent) {
    const { data } = event;
    const { tenantMembershipId } = data.metadata;
    if (!tenantMembershipId) throw new MissingTenantMembershipIdError();
    this.logger.info("Started handling new payment:::", tenantMembershipId);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      // Handle subscription created
      const tenantMembership = await this.tenantMembershipRepo.findOne(
        { filter: { id: { $eq: tenantMembershipId } } },
        repoOptions,
      );
      if (!tenantMembership) throw new TenantMembershipNotFoundError();

      await tenantMembership.addBillingCycle({ invoiceGateway: event.data.invoice });
      await tenantMembership.onInvoiceCreated();
      await this.tenantMembershipRepo.update(tenantMembership, repoOptions);
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle payment succeeded", error);
      throw error;
    }
  }
}
