import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IPaymentService } from "@features/app/services";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { UseCase, IUseCase, UseCaseContext, RepositoryOptions } from "@cbidigital/aqua-ddd";
import {
  IDatabaseUtil,
  IPaymentGateway,
  ITenantMembership,
  CustomerNotFoundError,
  IMembershipRepository,
  PaymentGatewayProvider,
  IPaymentGatewayFactory,
  ITenantMembershipBuilder,
  PaymentGatewaySubscription,
  ITenantMembershipRepository,
  CustomerIdIsNotProvidedError,
  MembershipCycleNotFoundError,
  TenantMembershipExternalMapping,
} from "@features/domain";

export type ActivateTenantMembershipUseCaseInput = {
  gateway: PaymentGatewayProvider;
  membershipId: string;
  membershipCycleId: string;
};

export type ActivateTenantMembershipUseCaseOutput = void;

const ActivateTenantMembershipUseCaseInputSchema = z.object({
  gateway: z.nativeEnum(PaymentGatewayProvider),
  membershipId: z.string(),
  membershipCycleId: z.string(),
});

export type IActivateTenantMembershipUseCase = IUseCase<
  ActivateTenantMembershipUseCaseInput,
  ActivateTenantMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.ACTIVATE_TENANT_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class ActivateTenantMembershipUseCase
  extends UseCase<
    ActivateTenantMembershipUseCaseInput,
    ActivateTenantMembershipUseCaseOutput,
    UseCaseContext
  >
  implements IActivateTenantMembershipUseCase
{
  private paymentGateway: IPaymentGateway;
  private readonly logger: ILogger;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.TENANT_MEMBERSHIP)
    protected readonly builder: ITenantMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.SERVICE.PAYMENT)
    protected readonly paymentService: IPaymentService,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
  ) {
    super();
    this.setMethods(this.processing);
    this.logger = new Logger(this.constructor.name);
  }

  processing = async (input: ActivateTenantMembershipUseCaseInput) => {
    const model = ActivateTenantMembershipUseCaseInputSchema.parse(input);
    const { gateway } = model;
    this.paymentGateway = this.paymentGatewayFactory.get(gateway);
    const customerId = this.context.auth?.authId;
    if (!customerId) throw new CustomerIdIsNotProvidedError();
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const [tenantMembership, customerInfo] = await Promise.all([
        this.getTenantMembership(model, repoOptions),
        this.paymentService.getExternalCustomer(customerId),
      ]);
      if (!customerInfo) throw new CustomerNotFoundError();
      const externalMembershipCycle = await this.getExternalMembership(model, repoOptions);

      // Create subscription
      const subscription = await this.paymentGateway.createSubscription({
        customerId: customerInfo.gatewayCustomerId,
        priceId: externalMembershipCycle.externalCycleId,
        metadata: { tenantMembershipId: tenantMembership.id },
      });

      // Create tenant membership mapping
      await this.createExternalTenantMembership(
        { tenantMembership, subscription, customerInfo, gateway },
        repoOptions,
      );
      await trx.commit();
      this.logger.info("Tenant membership activated", subscription);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  };

  private async getTenantMembership(
    input: ActivateTenantMembershipUseCaseInput,
    repoOptions: RepositoryOptions,
  ) {
    let tenantMembership = await this.tenantMembershipRepo.findOne(
      {
        filter: {
          tenantId: { $eq: this.context.tenantId },
          membershipId: { $eq: input.membershipId },
        },
      },
      repoOptions,
    );
    if (!tenantMembership) {
      tenantMembership = await this.builder.build();
      await tenantMembership.create({
        gateway: input.gateway,
        tenantId: this.context.tenantId!,
        membershipId: input.membershipId,
        membershipCycleId: input.membershipCycleId,
        createdBy: this.context.auth!.authId!,
      });
      await this.tenantMembershipRepo.create(tenantMembership, repoOptions);
    }

    return tenantMembership;
  }

  private async getExternalMembership(
    input: ActivateTenantMembershipUseCaseInput,
    repoOptions: RepositoryOptions,
  ) {
    const membershipCycle = await this.membershipRepo.findOneCycle(
      {
        filter: {
          id: { $eq: input.membershipCycleId },
          membershipId: { $eq: input.membershipId },
        },
      },
      repoOptions,
    );
    if (!membershipCycle) throw new MembershipCycleNotFoundError();
    const externalMembershipCycle = membershipCycle.getExternalMapping(input.gateway);
    if (!externalMembershipCycle) throw new MembershipCycleNotFoundError();
    return externalMembershipCycle;
  }

  private createExternalTenantMembership = async (
    input: {
      tenantMembership: ITenantMembership;
      subscription: PaymentGatewaySubscription;
      customerInfo: any;
      gateway: PaymentGatewayProvider;
    },
    repoOptions: RepositoryOptions,
  ) => {
    const tenantMembershipMapping = new TenantMembershipExternalMapping();
    await tenantMembershipMapping.create({
      provider: input.gateway,
      tenantMembershipId: input.tenantMembership.id,
      externalTenantMembershipId: input.subscription.id,
      externalCustomerId: input.customerInfo.id,
    });
    await this.tenantMembershipRepo.createExternalMapping(tenantMembershipMapping, repoOptions);
  };
}
