import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider, Logger } from "@heronjs/common";
import { UseCase, IUseCase, UseCaseContext, RepositoryOptions } from "@cbidigital/aqua-ddd";
import {
  IDatabaseUtil,
  ITenantMembership,
  ITenantMembershipBuilder,
  TenantMembershipStatusEnum,
  ITenantMembershipRepository,
  CreateTenantMembershipInput,
  MissingTrialPeriodDaysError,
  ActiveMembershipExistsError,
} from "@features/domain";

export type AssignTenantMembershipUseCaseInput = CreateTenantMembershipInput;

export type AssignTenantMembershipUseCaseOutput = { id: string };

const AssignTenantMembershipUseCaseInputSchema = z.object({
  tenantId: z.string(),
  membershipId: z.string(),
  customerId: z.string().optional(),
  membershipCycleId: z.string(),
  isTrial: z.boolean().optional(),
  trialPeriodDays: z.number().optional(),
  createdBy: z.string(),
});

export type IAssignTenantMembershipUseCase = IUseCase<
  AssignTenantMembershipUseCaseInput,
  AssignTenantMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.ASSIGN_TENANT_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class AssignTenantMembershipUseCase
  extends UseCase<
    AssignTenantMembershipUseCaseInput,
    AssignTenantMembershipUseCaseOutput,
    UseCaseContext
  >
  implements IAssignTenantMembershipUseCase
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.TENANT_MEMBERSHIP)
    protected readonly builder: ITenantMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: AssignTenantMembershipUseCaseInput) => {
    const model = AssignTenantMembershipUseCaseInputSchema.parse(input);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };

    try {
      // Handle current tenant membership
      await this.handleCurrentTenantMembership(model, repoOptions);

      // Create new tenant membership
      const entity = await this.createTenantMembership(model, repoOptions);
      await trx.commit();
      return { id: entity.id };
    } catch (error) {
      this.logger.error("Failed to assign tenant membership", error);
      await trx.rollback();
      throw error;
    }
  };

  /**
   * Handle current tenant membership
   */
  private async handleCurrentTenantMembership(
    model: AssignTenantMembershipUseCaseInput,
    repoOptions: RepositoryOptions,
  ): Promise<void> {
    const currentTenantMembership = await this.tenantMembershipRepo.findOne(
      {
        filter: {
          tenantId: { $eq: model.tenantId },
          status: {
            $is: [TenantMembershipStatusEnum.ACTIVE, TenantMembershipStatusEnum.TRIALING],
          },
        },
      },
      repoOptions,
    );

    if (currentTenantMembership) throw new ActiveMembershipExistsError();
  }

  /**
   * Create a new tenant membership
   */
  private async createTenantMembership(
    model: AssignTenantMembershipUseCaseInput,
    repoOptions: RepositoryOptions,
  ): Promise<ITenantMembership> {
    // Create tenant membership
    const isMissingTrialPeriodDays = model.isTrial && !model.trialPeriodDays;
    if (isMissingTrialPeriodDays) throw new MissingTrialPeriodDaysError();
    const entity = await this.builder.build();
    await entity.create(model);

    // Save tenant membership with its billing cycles
    await this.tenantMembershipRepo.create(entity, repoOptions);

    return entity;
  }
}
