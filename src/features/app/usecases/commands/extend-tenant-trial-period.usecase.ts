import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  ITenantMembershipBuilder,
  ITenantMembershipRepository,
  TenantMembershipNotFoundError,
} from "@features/domain";

export type ExtendTenantTrialPeriodUseCaseInput = {
  tenantMembershipId: string;
  trialPeriodDays: number;
};

export type ExtendTenantTrialPeriodUseCaseOutput = void;

const ExtendTenantTrialPeriodUseCaseInputSchema = z.object({
  trialPeriodDays: z.number(),
  tenantMembershipId: z.string(),
});

export type IExtendTenantTrialPeriodUseCase = IUseCase<
  ExtendTenantTrialPeriodUseCaseInput,
  ExtendTenantTrialPeriodUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.EXTEND_TENANT_TRIAL_PERIOD,
  scope: Lifecycle.Transient,
})
export class ExtendTenantTrialPeriodUseCase
  extends UseCase<
    ExtendTenantTrialPeriodUseCaseInput,
    ExtendTenantTrialPeriodUseCaseOutput,
    UseCaseContext
  >
  implements IExtendTenantTrialPeriodUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.TENANT_MEMBERSHIP)
    protected readonly builder: ITenantMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipRepo: ITenantMembershipRepository,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: ExtendTenantTrialPeriodUseCaseInput) => {
    const model = ExtendTenantTrialPeriodUseCaseInputSchema.parse(input);
    const currentTenantMembership = await this.tenantMembershipRepo.findOne({
      filter: {
        id: { $eq: model.tenantMembershipId },
        isActive: { $eq: true },
      },
    });
    if (!currentTenantMembership) throw new TenantMembershipNotFoundError();
    await currentTenantMembership.extendTrialPeriod(model.trialPeriodDays);
    await this.tenantMembershipRepo.update(currentTenantMembership);
  };
}
