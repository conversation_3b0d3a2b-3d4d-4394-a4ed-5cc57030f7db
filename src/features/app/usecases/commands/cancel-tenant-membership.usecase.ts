import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import {
  IPaymentGateway,
  IPaymentGatewayFactory,
  ITenantMembershipRepository,
  TenantMembershipNotFoundError,
} from "@features/domain";

export type CancelTenantMembershipUseCaseInput = { tenantMembershipId: string };
export type CancelTenantMembershipUseCaseOutput = { id: string };

const CancelTenantMembershipUseCaseInputSchema = z.object({
  tenantMembershipId: z.string(),
});

export type ICancelTenantMembershipUseCase = IUseCase<
  CancelTenantMembershipUseCaseInput,
  CancelTenantMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.CANCEL_TENANT_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class CancelTenantMembershipUseCase
  extends UseCase<
    CancelTenantMembershipUseCaseInput,
    CancelTenantMembershipUseCaseOutput,
    UseCaseContext
  >
  implements ICancelTenantMembershipUseCase
{
  private readonly logger: ILogger;
  private paymentGateway: IPaymentGateway;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP)
    protected readonly repo: ITenantMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    super();
    this.setMethods(this.processing);
    this.logger = new Logger(this.constructor.name);
  }

  processing = async (input: CancelTenantMembershipUseCaseInput) => {
    const model = CancelTenantMembershipUseCaseInputSchema.parse(input);
    try {
      const tenantMembership = await this.repo.findOne({
        filter: {
          id: { $eq: model.tenantMembershipId },
          isActive: { $eq: true },
        },
      });
      if (!tenantMembership) throw new TenantMembershipNotFoundError();
      this.paymentGateway = this.paymentGatewayFactory.get(tenantMembership.gateway!);
      if (!this.paymentGateway) throw new Error("Payment gateway not found");
      const externalMapping = tenantMembership.getTenantMembershipExternalMapping(
        tenantMembership.gateway!,
      );
      if (!externalMapping) throw new TenantMembershipNotFoundError();
      await this.paymentGateway.cancelSubscription({
        subscriptionId: externalMapping.externalTenantMembershipId,
      });
      return { id: tenantMembership.id };
    } catch (error) {
      this.logger.error("Failed to cancel tenant membership", error);
      throw error;
    }
  };
}
