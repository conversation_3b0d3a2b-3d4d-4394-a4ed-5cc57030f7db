import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ITenantMembershipDao } from "@features/infra";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  TenantMembershipDto,
  MembershipNotFoundError,
  ITenantMembershipMapper,
} from "@features/domain";

export type GetTenantMembershipUseCaseInput = { id: string };

export type GetTenantMembershipUseCaseOutput = TenantMembershipDto;

export type IGetTenantMembershipUseCase = IUseCase<
  GetTenantMembershipUseCaseInput,
  GetTenantMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_TENANT_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class GetTenantMembershipUseCase
  extends UseCase<GetTenantMembershipUseCaseInput, GetTenantMembershipUseCaseOutput, UseCaseContext>
  implements IGetTenantMembershipUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP)
    protected readonly dao: ITenantMembershipDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP)
    protected readonly mapper: ITenantMembershipMapper,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetTenantMembershipUseCaseInput) => {
    const dto = await this.dao.findOne({ filter: { id: { $eq: input.id } } });
    if (!dto) throw new MembershipNotFoundError();

    return dto;
  };
}
