import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ITenantMembershipDao } from "@features/infra";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { TenantMembershipDto, tenantMembershipStatusOrder } from "@features/domain";

export type GetCurrentTenantMembershipUseCaseInput = {
  tenantId: string;
};

export type GetCurrentTenantMembershipUseCaseOutput = TenantMembershipDto;

const GetCurrentTenantMembershipUseCaseInputSchema = z.object({
  tenantId: z.string(),
});

export type IGetCurrentTenantMembershipUseCase = IUseCase<
  GetCurrentTenantMembershipUseCaseInput,
  GetCurrentTenantMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_CURRENT_TENANT_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class GetCurrentTenantMembershipUseCase
  extends UseCase<
    GetCurrentTenantMembershipUseCaseInput,
    GetCurrentTenantMembershipUseCaseOutput,
    UseCaseContext
  >
  implements IGetCurrentTenantMembershipUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP)
    protected readonly dao: ITenantMembershipDao,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetCurrentTenantMembershipUseCaseInput) => {
    const { tenantId } = GetCurrentTenantMembershipUseCaseInputSchema.parse(input);

    // Find active tenant membership for the current tenant
    const dtos = await this.dao.find({
      filter: { tenantId: { $eq: tenantId } },
    });
    const [dto] = dtos.sort((a, b) => {
      const aStatus = tenantMembershipStatusOrder[a.status!];
      const bStatus = tenantMembershipStatusOrder[b.status!];
      return aStatus - bStatus;
    });

    return { data: dto ?? null };
  };
}
