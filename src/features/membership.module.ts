import { Module } from "@heronjs/common";
import {
  PaymentF<PERSON><PERSON><PERSON>ler,
  InvoiceCreated<PERSON><PERSON><PERSON>,
  WebhookHandlerFactory,
  PaymentSucceededHandler,
  CreateMembershipUseCase,
  UpdateMembershipUseCase,
  CreateCapabilityUseCase,
  GetMembershipByIdUseCase,
  GetTenantMembershipUseCase,
  GetListOfMembershipsUseCase,
  GetListOfCapabilitiesUseCase,
  AssignTenantMembershipUseCase,
  CancelTenantMembershipUseCase,
  ExtendTenantTrialPeriodUseCase,
  ActivateTenantMembershipUseCase,
  TenantMembershipCancelledHandler,
  GetMembershipCapabilitiesUseCase,
} from "@features/app";
import {
  PaymentWebhook,
  AdminCapabilityRest,
  AdminMembershipRest,
  TenantMembershipRest,
  AdminTenantMembershipRest,
  InternalTenantMembershipRest,
} from "@features/presentation";
import {
  CapabilityMapper,
  MembershipMapper,
  CapabilityBuilder,
  MembershipBuilder,
  MembershipCycleMapper,
  TenantMembershipMapper,
  <PERSON>ingCycleMapper,
  TenantMembershipBuilder,
  MembershipCapabilityMapper,
  CustomerExternalMappingMapper,
  MembershipExternalMappingMapper,
  MembershipCycleExternalMappingMapper,
  TenantMembershipExternalMappingMapper,
} from "@features/domain";
import {
  DatabaseUtil,
  CapabilityDao,
  MembershipDao,
  PaymentService,
  BasicRetryUtil,
  UserProfileService,
  MembershipCycleDao,
  TenantMembershipDao,
  BillingCycleDao,
  MembershipRepository,
  PaypalPaymentGateway,
  StripePaymentGateway,
  CapabilityRepository,
  PaymentGatewayFactory,
  MembershipCapabilityDao,
  TenantMembershipRepository,
  CustomerExternalMappingDao,
  MembershipExternalMappingDao,
  CustomerExternalMappingRepository,
  MembershipCycleExternalMappingDao,
  TenantMembershipExternalMappingDao,
} from "@features/infra";

@Module({
  controllers: [
    AdminMembershipRest,
    AdminTenantMembershipRest,
    AdminCapabilityRest,
    InternalTenantMembershipRest,
    TenantMembershipRest,
    PaymentWebhook,
  ],
  providers: [
    // Mappers
    CapabilityMapper,
    MembershipMapper,
    MembershipCycleMapper,
    TenantMembershipMapper,
    BillingCycleMapper,
    MembershipCapabilityMapper,
    CustomerExternalMappingMapper,
    MembershipExternalMappingMapper,
    MembershipCycleExternalMappingMapper,
    TenantMembershipExternalMappingMapper,

    // Builders
    CapabilityBuilder,
    MembershipBuilder,
    TenantMembershipBuilder,

    // DAOs
    CapabilityDao,
    MembershipDao,
    MembershipCycleDao,
    TenantMembershipDao,
    BillingCycleDao,
    MembershipCapabilityDao,
    CustomerExternalMappingDao,
    MembershipExternalMappingDao,
    MembershipCycleExternalMappingDao,
    TenantMembershipExternalMappingDao,

    // Repositories
    CapabilityRepository,
    MembershipRepository,
    TenantMembershipRepository,
    CustomerExternalMappingRepository,

    // Membership Use Cases
    CreateMembershipUseCase,
    UpdateMembershipUseCase,
    GetMembershipByIdUseCase,
    GetListOfMembershipsUseCase,

    // Tenant Membership Use Cases
    GetTenantMembershipUseCase,
    AssignTenantMembershipUseCase,
    ExtendTenantTrialPeriodUseCase,
    ActivateTenantMembershipUseCase,
    CancelTenantMembershipUseCase,

    // Capability Use Cases
    CreateCapabilityUseCase,
    GetListOfCapabilitiesUseCase,

    // Membership Capability Use Cases
    GetMembershipCapabilitiesUseCase,

    // Utils
    BasicRetryUtil,
    DatabaseUtil,

    // Factories
    PaymentGatewayFactory,
    WebhookHandlerFactory,

    // External Services
    PaymentService,
    UserProfileService,

    // Payment Gateways
    StripePaymentGateway,
    PaypalPaymentGateway,

    // Handlers
    PaymentFailedHandler,
    InvoiceCreatedHandler,
    PaymentSucceededHandler,
    TenantMembershipCancelledHandler,
  ],
})
// @Transporters([KafkaAdapter])
export class MembershipModule {}
