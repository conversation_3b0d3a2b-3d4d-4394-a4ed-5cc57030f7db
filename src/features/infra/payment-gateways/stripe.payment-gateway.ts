import Stripe from "stripe";
import { PAYMENT_GATEWAY_CONFIG } from "@configs";
import { IRetryUtil } from "@features/infra/utils";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import {
  Invoice,
  PaymentEvent,
  EventPayload,
  Subscription,
  IPaymentGateway,
  PaymentGatewayPrice,
  PaymentGatewayProduct,
  PaymentGatewaySubscription,
  CreatePaymentGatewayPriceInput,
  CreatePaymentGatewayProductInput,
  CreatePaymentGatewayCustomerInput,
  UpdatePaymentGatewayCustomerInput,
  CreatePaymentGatewayCustomerOutput,
  UpdatePaymentGatewayCustomerOutput,
  CancelPaymentGatewaySubscriptionInput,
  CreatePaymentGatewaySubscriptionInput,
  UpdatePaymentGatewaySubscriptionInput,
  CancelPaymentGatewaySubscriptionOutput,
  UpdatePaymentGatewaySubscriptionOutput,
} from "@features/domain";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.PAYMENT_GATEWAY.STRIPE,
  scope: Lifecycle.Singleton,
})
export class StripePaymentGateway implements IPaymentGateway {
  private readonly stripe: Stripe;
  private readonly logger: ILogger;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.RETRY)
    private readonly retryUtil: IRetryUtil,
  ) {
    this.logger = new Logger(this.constructor.name);
    this.stripe = new Stripe(PAYMENT_GATEWAY_CONFIG.SECRET_KEY.STRIPE, {
      apiVersion: "2025-04-30.basil",
    });
  }

  async parseEvent(input: EventPayload): Promise<PaymentEvent> {
    try {
      const { body } = input;
      // const sig = headers["stripe-signature"];
      // const secret = PAYMENT_GATEWAY_CONFIG.WEBHOOK_SECRET.STRIPE;
      // const event = this.stripe.webhooks.constructEvent(body, sig, secret);
      const event = body;
      switch (event.type) {
        case "invoice.payment_succeeded":
          return {
            gateway: PAYMENT_GATEWAY_CONFIG.PROVIDER,
            type: event.type,
            data: {
              subscription: event.data.object.parent?.subscription_details?.subscription,
              invoice: event.data.object.id,
              customer: event.data.object.customer,
              metadata: event.data.object.parent.subscription_details.metadata,
            },
          };
        case "customer.subscription.deleted":
          return {
            gateway: PAYMENT_GATEWAY_CONFIG.PROVIDER,
            type: event.type,
            data: {
              subscriptionId: event.data.object.id,
            },
          };
        case "invoice.created":
          return {
            gateway: PAYMENT_GATEWAY_CONFIG.PROVIDER,
            type: event.type,
            data: {
              subscription: event.data.object.parent?.subscription_details?.subscription,
              invoice: event.data.object.id,
              customer: event.data.object.customer,
              metadata: {
                tenantMembershipId:
                  event.data.object.parent?.subscription_details?.metadata?.tenantMembershipId,
              },
            },
          };
        default:
          return {
            gateway: PAYMENT_GATEWAY_CONFIG.PROVIDER,
            type: event.type,
            data: event.data.object,
          };
      }
    } catch (error) {
      throw new Error(`Failed to parse event: ${error}`);
    }
  }

  async getSubscription(subscriptionId: string): Promise<Subscription> {
    try {
      const sub = await this.retryUtil.retry(() =>
        this.stripe.subscriptions.retrieve(subscriptionId),
      );
      const [subItem] = sub.items.data;
      const { current_period_start, current_period_end } = subItem;
      return {
        id: sub.id,
        customer: sub.customer as string,
        invoice: sub.latest_invoice as string,
        periodStart: current_period_start,
        periodEnd: current_period_end,
        metadata: sub.metadata,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve subscription: ${error}`);
    }
  }

  async getInvoice(invoiceId: string): Promise<Invoice> {
    try {
      const invoice = await this.stripe.invoices.retrieve(invoiceId);
      return { id: invoice.id!, created: invoice.created };
    } catch (error) {
      throw new Error(`Failed to retrieve invoice: ${error}`);
    }
  }

  async createProduct(payload: CreatePaymentGatewayProductInput): Promise<PaymentGatewayProduct> {
    const product = await this.retryUtil.retry(() => this.stripe.products.create(payload));
    return { id: product.id };
  }

  async createPrice(payload: CreatePaymentGatewayPriceInput): Promise<PaymentGatewayPrice> {
    try {
      const price = await this.retryUtil.retry(() =>
        this.stripe.prices.create({
          unit_amount: payload.amount,
          currency: payload.currency,
          product: payload.productId,
          recurring: {
            interval: payload.recurring.interval,
            interval_count: payload.recurring.intervalCount,
          },
          metadata: payload.metadata,
        }),
      );
      return { id: price.id };
    } catch (err: any) {
      throw new Error(`Failed to create price: ${err.message}`);
    }
  }

  async createSubscription(
    input: CreatePaymentGatewaySubscriptionInput,
  ): Promise<PaymentGatewaySubscription> {
    try {
      const subscription = await this.retryUtil.retry(() =>
        this.stripe.subscriptions.create({
          customer: input.customerId,
          items: [{ price: input.priceId }],
          metadata: input.metadata,
        }),
      );
      return {
        id: subscription.id,
        customerId: subscription.customer as string,
      };
    } catch (error) {
      throw new Error(`Failed to create subscription: ${error}`);
    }
  }

  async cancelSubscription(
    input: CancelPaymentGatewaySubscriptionInput,
  ): Promise<CancelPaymentGatewaySubscriptionOutput> {
    try {
      const subscription = await this.retryUtil.retry(() =>
        this.stripe.subscriptions.update(input.subscriptionId, {
          cancel_at_period_end: true,
        }),
      );
      return { id: subscription.id };
    } catch (error) {
      throw new Error(`Failed to cancel subscription: ${error}`);
    }
  }

  updateSubscription(
    _input: UpdatePaymentGatewaySubscriptionInput,
  ): Promise<UpdatePaymentGatewaySubscriptionOutput> {
    throw new Error("Method not implemented.");
  }

  createCustomer(
    _input: CreatePaymentGatewayCustomerInput,
  ): Promise<CreatePaymentGatewayCustomerOutput> {
    throw new Error("Method not implemented.");
  }
  updateCustomer(
    _input: UpdatePaymentGatewayCustomerInput,
  ): Promise<UpdatePaymentGatewayCustomerOutput> {
    throw new Error("Method not implemented.");
  }
}
