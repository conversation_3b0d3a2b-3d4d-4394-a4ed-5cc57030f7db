import { Inject, Optional, Nullable, Lifecycle, Repository, DataSource } from "@heronjs/common";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  IMembership,
  MembershipDto,
  IMembershipCycle,
  IMembershipMapper,
  MembershipCycleDto,
  IMembershipRepository,
  IMembershipCycleMapper,
  IMembershipExternalMapping,
  MembershipExternalMappingDto,
  IMembershipCycleExternalMapping,
  IMembershipExternalMappingMapper,
  MembershipCycleExternalMappingDto,
  IMembershipCycleExternalMappingMapper,
} from "@features/domain";
import {
  IMembershipDao,
  IMembershipCycleDao,
  IMembershipCapabilityDao,
  IMembershipExternalMappingDao,
  IMembershipCycleExternalMappingDao,
} from "@features/infra/databases";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";

@Repository({
  token: MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class MembershipRepository
  extends BaseRepository<IMembership>
  implements IMembershipRepository
{
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP)
    private readonly membershipDao: IMembershipDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CYCLE)
    private readonly cycleDao: IMembershipCycleDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CAPABILITY)
    private readonly capabilityDao: IMembershipCapabilityDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_EXTERNAL_MAPPING)
    private readonly membershipExternalMappingDao: IMembershipExternalMappingDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING)
    private readonly membershipCycleExternalMappingDao: IMembershipCycleExternalMappingDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP)
    protected readonly membershipMapper: IMembershipMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_EXTERNAL_MAPPING)
    protected readonly externalMappingMapper: IMembershipExternalMappingMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE_EXTERNAL_MAPPING)
    protected readonly cycleExternalMappingMapper: IMembershipCycleExternalMappingMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE)
    protected readonly cycleMapper: IMembershipCycleMapper,
  ) {
    super({ db });
  }

  async create(entity: IMembership, options?: RepositoryOptions): Promise<IMembership> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.membershipMapper.fromEntityToDto(entity);
    await this.membershipDao.create(dto, options);
    if (dto.upsertedCycles?.length) {
      await this.cycleDao.createList(dto.upsertedCycles, options);
    }
    if (dto.upsertedCapabilities?.length) {
      await this.capabilityDao.createList(dto.upsertedCapabilities, options);
    }
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: IMembership, options?: RepositoryOptions): Promise<IMembership> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.membershipMapper.fromEntityToDto(entity);
    await this.membershipDao.update(dto, { ...options, trx });
    const promises = [];
    if (dto.upsertedCycles?.length) {
      promises.push(this.cycleDao.upsertList(dto.upsertedCycles, { ...options, trx }));
    }
    if (dto.deletedCycles?.length) {
      const deletedIds = dto.deletedCycles.map((item) => item.id);
      promises.push(this.cycleDao.deleteList(deletedIds, { ...options, trx }));
    }
    if (dto.upsertedCapabilities?.length) {
      promises.push(
        this.capabilityDao.upsertList(dto.upsertedCapabilities, {
          ...options,
          trx,
        }),
      );
    }
    if (dto.deletedCapabilities?.length) {
      const deletedIds = dto.deletedCapabilities.map((item) => item.id);
      promises.push(this.capabilityDao.deleteList(deletedIds, { ...options, trx }));
    }
    await Promise.all(promises);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: IMembership, options?: RepositoryOptions): Promise<IMembership> {
    await this.withTransaction(async (trx) => {
      await this.membershipDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<IMembership[]> {
    const dtos = await this.membershipDao.find(input, options);
    const entities = await this.membershipMapper.fromDtosToEntities(dtos as MembershipDto[]);
    return entities;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<IMembership>> {
    const dto = await this.membershipDao.findOne(input, options);
    const entity = dto ? await this.membershipMapper.fromDtoToEntity(dto as MembershipDto) : dto;
    return entity;
  }

  async upsertList(entities: IMembership[], options: RepositoryOptions) {
    const dtos = await this.membershipMapper.fromEntitiesToDtos(entities);
    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.membershipDao.count(input, options);
  }

  async createExternalMapping(
    entity: IMembershipExternalMapping,
    options?: RepositoryOptions,
  ): Promise<IMembershipExternalMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.externalMappingMapper.fromEntityToDto(entity);
    await this.membershipExternalMappingDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async findOneExternalMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipExternalMapping>> {
    const dtos = await this.membershipExternalMappingDao.findOne(input, options);
    if (!dtos) return null;
    const entity = await this.externalMappingMapper.fromDtoToEntity(
      dtos as MembershipExternalMappingDto,
    );
    return entity;
  }

  async createCycleExternalMapping(
    entity: IMembershipCycleExternalMapping,
    options?: RepositoryOptions,
  ): Promise<IMembershipCycleExternalMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.cycleExternalMappingMapper.fromEntityToDto(entity);
    await this.membershipCycleExternalMappingDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async findOneCycleExternalMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCycleExternalMapping>> {
    const dtos = await this.membershipCycleExternalMappingDao.findOne(input, options);
    if (!dtos) return null;
    const entity = await this.cycleExternalMappingMapper.fromDtoToEntity(
      dtos as MembershipCycleExternalMappingDto,
    );
    return entity;
  }

  async findOneCycle(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCycle>> {
    const dtos = await this.cycleDao.findOne(input, options);
    if (!dtos) return null;
    const entity = await this.cycleMapper.fromDtoToEntity(dtos as MembershipCycleDto);
    return entity;
  }
}
