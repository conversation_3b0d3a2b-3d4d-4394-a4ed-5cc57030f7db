import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  ITenantMembership,
  TenantMembershipDto,
  ITenantMembershipMapper,
  ITenantMembershipRepository,
  ITenantMembershipExternalMapping,
  TenantMembershipExternalMappingDto,
  ITenantMembershipExternalMappingMapper,
} from "@features/domain";
import { Inject, Optional, Nullable, Lifecycle, Repository, DataSource } from "@heronjs/common";
import {
  IBillingCycleDao,
  ITenantMembershipDao,
  ITenantMembershipExternalMappingDao,
} from "@features/infra/databases";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";

@Repository({
  token: MEMBERSHIP_INJECT_TOKENS.REPOSITORY.TENANT_MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipRepository
  extends BaseRepository<ITenantMembership>
  implements ITenantMembershipRepository
{
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP)
    private readonly tenantMembershipDao: ITenantMembershipDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP_EXTERNAL_MAPPING)
    private readonly tenantMembershipExternalMappingDao: ITenantMembershipExternalMappingDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.BILLING_CYCLE)
    private readonly billingCycleDao: IBillingCycleDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP)
    protected readonly tenantMembershipMapper: ITenantMembershipMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.TENANT_MEMBERSHIP_EXTERNAL_MAPPING)
    protected readonly tenantMembershipExternalMappingMapper: ITenantMembershipExternalMappingMapper,
  ) {
    super({ db });
  }

  async create(entity: ITenantMembership, options?: RepositoryOptions): Promise<ITenantMembership> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.tenantMembershipMapper.fromEntityToDto(entity);
    await this.tenantMembershipDao.create(dto, { ...options, trx });
    if (dto.billingCycles?.length) {
      await this.billingCycleDao.createList(dto.billingCycles, {
        ...options,
        trx,
      });
    }
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: ITenantMembership, options?: RepositoryOptions): Promise<ITenantMembership> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.tenantMembershipMapper.fromEntityToDto(entity);
    await this.tenantMembershipDao.update(dto, { ...options, trx });
    if (dto.billingCycles?.length) {
      await this.billingCycleDao.upsertList(dto.billingCycles, {
        ...options,
        trx,
      });
    }
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: ITenantMembership, options?: RepositoryOptions): Promise<ITenantMembership> {
    await this.withTransaction(async (trx) => {
      await this.tenantMembershipDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<ITenantMembership[]> {
    const dtos = await this.tenantMembershipDao.find(input, options);
    const entities = await this.tenantMembershipMapper.fromDtosToEntities(
      dtos as TenantMembershipDto[],
    );
    return entities;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<ITenantMembership>> {
    const dto = await this.tenantMembershipDao.findOne(input, options);
    const entity = dto
      ? await this.tenantMembershipMapper.fromDtoToEntity(dto as TenantMembershipDto)
      : dto;
    return entity;
  }

  async findOneExternalMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<ITenantMembershipExternalMapping>> {
    const dto = await this.tenantMembershipExternalMappingDao.findOne(input, options);
    if (!dto) return null;
    const entity = await this.tenantMembershipExternalMappingMapper.fromDtoToEntity(
      dto as TenantMembershipExternalMappingDto,
    );
    return entity;
  }

  async createExternalMapping(
    entity: ITenantMembershipExternalMapping,
    options?: RepositoryOptions,
  ): Promise<ITenantMembershipExternalMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.tenantMembershipExternalMappingMapper.fromEntityToDto(entity);
    await this.tenantMembershipExternalMappingDao.create(dto, {
      ...options,
      trx,
    });
    if (!options?.trx) await trx.commit();

    return entity;
  }
}
