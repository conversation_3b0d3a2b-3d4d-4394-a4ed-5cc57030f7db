// import axios from "axios";
// import { API_URL_CONFIG } from "@configs";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { CustomerDTO, IPaymentService } from "@features/app";
import { Lifecycle, Logger, Provider } from "@heronjs/common";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.SERVICE.PAYMENT,
  scope: Lifecycle.Singleton,
})
export class PaymentService implements IPaymentService {
  private readonly logger = new Logger(this.constructor.name);

  async getExternalCustomer(id: string): Promise<CustomerDTO> {
    try {
      // const url = `${API_URL_CONFIG.PAYMENT_API_URL}/internal/customers/${id}`;
      // const data = await axios.get(url);
      // const users = data.data.data;
      // return users;
      console.log("Getting customer for id:", id);
      return {
        id: "1",
        gatewayCustomerId: "cus_SM8PkU0W8oIKLp",
        defaultPaymentMethodId: "pm_1Nv0JZK2i9i0JZK2i9i0JZK2",
      };
    } catch (err: any) {
      this.logger.error("Failed to get customer info", err);
      throw err;
    }
  }
}
