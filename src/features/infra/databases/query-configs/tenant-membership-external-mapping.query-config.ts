import { QueryConfig } from "@cbidigital/aqua-ddd";
import { TenantMembershipExternalMappingDto } from "@features/domain";
import { ExternalTenantMembershipIdFilter } from "@features/infra/databases/filters/tenant-membership-external-mapping";
import { TenantIdFilter, TenantMembershipIdFilter } from "@features/infra/databases/filters";

export const TenantMembershipExternalMappingQueryConfig: QueryConfig<
  keyof TenantMembershipExternalMappingDto
> = {
  id: {
    sortable: true,
    filterable: true,
    filterClass: TenantMembershipIdFilter,
  },

  externalTenantMembershipId: {
    sortable: true,
    filterable: true,
    filterClass: ExternalTenantMembershipIdFilter,
  },

  tenantMembershipId: {
    sortable: true,
    filterable: true,
    filterClass: TenantIdFilter,
  },
};
