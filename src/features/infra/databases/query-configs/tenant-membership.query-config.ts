import { QueryConfig } from "@cbidigital/aqua-ddd";
import { TenantMembershipDto } from "@features/domain";
import {
  TenantIdFilter,
  TenantMembershipIdFilter,
  TenantMembershipStatusFilter,
  TenantMembershipCycleIdFilter,
} from "@features/infra/databases/filters";

export const TenantMembershipQueryConfig: QueryConfig<keyof TenantMembershipDto> = {
  id: {
    sortable: true,
    filterable: true,
    filterClass: TenantMembershipIdFilter,
  },

  tenantId: {
    sortable: true,
    filterable: true,
    filterClass: TenantIdFilter,
  },

  membershipCycleId: {
    sortable: true,
    filterable: true,
    filterClass: TenantMembershipCycleIdFilter,
  },

  status: {
    sortable: true,
    filterable: true,
    filterClass: TenantMembershipStatusFilter,
  },
};
