import { MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";
import { Filter, FilterPayload, FilterTypes } from "@cbidigital/aqua-ddd";

export class ExternalTenantMembershipIdFilter extends Filter {
  constructor(payload: FilterPayload) {
    super({
      type: FilterTypes.STRING,
      compareField: `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING}.external_tenant_membership_id`,
      ...payload,
    });
  }
}
