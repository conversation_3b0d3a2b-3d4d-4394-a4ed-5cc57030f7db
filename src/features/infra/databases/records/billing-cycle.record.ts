import { Nullable } from "@heronjs/common";
import { BillingCycleStatusEnum } from "@features/domain";

export type BillingCycleRecord = {
  id: string;
  payment_method_id: Nullable<string>;
  tenant_membership_id: string;
  period_start: Nullable<number>;
  period_end: Nullable<number>;
  status: BillingCycleStatusEnum;
  invoice_gateway: string;
  created_at: Date;
  updated_at: Nullable<Date>;
};
