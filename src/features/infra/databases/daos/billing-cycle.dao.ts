import { <PERSON><PERSON> } from "knex";
import { BillingCycleDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { BillingCycleRecord } from "@features/infra/databases/records";
import { Billing<PERSON>ycleRecordMapper } from "@features/infra/databases/record-mappers";
import { BillingCycleQueryConfig } from "@features/infra/databases/query-configs";
import {
  BaseDao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInput,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_TABLE_NAMES,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
} from "@constants";

export interface IBillingCycleDao extends IBaseDao<BillingCycleDto, BillingCycleRecord> {
  update(
    entity: Partial<BillingCycleDto>,
    options?: RepositoryOptions,
  ): Promise<Partial<BillingCycleDto>>;
}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.BILLING_CYCLE,
  scope: Lifecycle.Singleton,
})
export class BillingCycleDao
  extends BaseDao<BillingCycleDto, BillingCycleRecord>
  implements IBillingCycleDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [
    "tenant_membership_id",
    "payment_method_id",
    "period_start",
    "period_end",
    "status",
    "invoice_gateway",
    "created_at",
    "updated_at",
  ];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.BILLING_CYCLE,
      recordMapper: new BillingCycleRecordMapper(),
    });
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select("*");
    return query;
  }

  async find(
    payload: QueryInput & { search?: string },
    options?: RepositoryOptions,
  ): Promise<Partial<BillingCycleDto>[]> {
    const { offset, limit, sort, filter, search } = payload;
    const client = this.db.getClient(options?.tenantId);
    const query = client.from(this.tableName).modify(this.buildSelectClause);
    if (search) this.applySearch(query, search);
    if (filter) DaoUtils.applyFilter(filter, BillingCycleQueryConfig, query);
    if (offset !== undefined) query.offset(offset);
    if (limit !== undefined) query.limit(limit);
    if (sort) DaoUtils.applySort(sort, BillingCycleQueryConfig, query);
    const records = await query;
    const dtos = this.recordMapper.fromRecordsToDtos(records);
    return dtos;
  }

  private applySearch(query: Knex.QueryBuilder, search: string) {
    return query.where((builder) => {
      builder.where(MEMBERSHIP_MODULE_COLUMN_NAMES.BILLING_CYCLE.ID, "like", `%${search}%`);
    });
  }

  async update(
    dto: Partial<BillingCycleDto>,
    options: RepositoryOptions,
  ): Promise<Partial<BillingCycleDto>> {
    const client = this.db.getClient(options?.tenantId);
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client
      .table(this.tableName)
      .where(MEMBERSHIP_MODULE_COLUMN_NAMES.BILLING_CYCLE.ID, dto.id!)
      .update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  async upsertList(dtos: Partial<BillingCycleDto>[], options?: RepositoryOptions) {
    const client = this.db.getClient();
    const conflicColumns = ["id"];
    const records = this.recordMapper.fromDtosToRecords(dtos);
    const query = client
      .table(this.tableName)
      .insert(records)
      .onConflict(conflicColumns)
      .merge(this.mergedColumns);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dtos;
  }
}
