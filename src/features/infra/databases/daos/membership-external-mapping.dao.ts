import { MembershipExternalMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle } from "@heronjs/common";
import { MembershipExternalMappingRecord } from "@features/infra/databases/records";
import { MembershipExternalMappingRecordMapper } from "@features/infra/databases/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS, MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";

export interface IMembershipExternalMappingDao
  extends IBaseDao<MembershipExternalMappingDto, MembershipExternalMappingRecord> {
  create(
    dto: MembershipExternalMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipExternalMappingDto>;
}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipExternalMappingDao
  extends BaseDao<MembershipExternalMappingDto, MembershipExternalMappingRecord>
  implements IMembershipExternalMappingDao
{
  // private readonly logger = new Logger(this.constructor.name);
  // private readonly mergedColumns = [];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_EXTERNAL_MAPPING,
      recordMapper: new MembershipExternalMappingRecordMapper(),
    });
  }

  async create(
    dto: MembershipExternalMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipExternalMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
