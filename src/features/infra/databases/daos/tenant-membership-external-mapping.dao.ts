import { <PERSON><PERSON> } from "knex";
import { TenantMembershipExternalMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger, Optional } from "@heronjs/common";
import { TenantMembershipExternalMappingRecord } from "@features/infra/databases/records";
import { TenantMembershipExternalMappingQueryConfig } from "@features/infra/databases/query-configs";
import { TenantMembershipExternalMappingRecordMapper } from "@features/infra/databases/record-mappers";
import {
  BaseDao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
  MEMBERSHIP_MODULE_TABLE_NAMES,
} from "@constants";

export interface ITenantMembershipExternalMappingDao
  extends IBaseDao<TenantMembershipExternalMappingDto, TenantMembershipExternalMappingRecord> {
  create(
    dto: TenantMembershipExternalMappingDto,
    options?: RepositoryOptions,
  ): Promise<TenantMembershipExternalMappingDto>;
}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.TENANT_MEMBERSHIP_EXTERNAL_MAPPING,
  scope: Lifecycle.Singleton,
})
export class TenantMembershipExternalMappingDao
  extends BaseDao<TenantMembershipExternalMappingDto, TenantMembershipExternalMappingRecord>
  implements ITenantMembershipExternalMappingDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING,
      recordMapper: new TenantMembershipExternalMappingRecordMapper(),
    });
  }

  async create(
    dto: TenantMembershipExternalMappingDto,
    options?: RepositoryOptions,
  ): Promise<TenantMembershipExternalMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select("*");
    return query;
  }

  private buildJoinClause(builder: Knex.QueryBuilder) {
    builder.leftJoin(
      MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP_EXTERNAL_MAPPING.TENANT_MEMBERSHIP_ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.TENANT_MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.TENANT_MEMBERSHIP.ID}`,
    );
  }

  async findOne(
    payload?: QueryInputFindOne<TenantMembershipExternalMappingDto>,
    options?: RepositoryOptions,
  ): Promise<Optional<Partial<TenantMembershipExternalMappingDto>>> {
    const client = this.db.getClient(options?.tenantId);
    const query = client
      .from(this.tableName)
      .modify(this.buildSelectClause)
      .modify(this.buildJoinClause);
    if (payload?.filter)
      DaoUtils.applyFilter(payload.filter, TenantMembershipExternalMappingQueryConfig, query);
    if (options?.trx) query.transacting(options.trx);
    const record = await query.first();
    if (!record) return undefined;
    return this.recordMapper.fromRecordToDto(record);
  }
}
