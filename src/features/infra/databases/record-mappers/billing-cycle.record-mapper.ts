import { BillingCycleDto } from "@features/domain";
import { BillingCycleRecord } from "@features/infra/databases/records";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";

export type IBillingCycleRecordMapper = IRecordMapper<BillingCycleDto, BillingCycleRecord>;

export class BillingCycleRecordMapper
  extends BaseR<PERSON>ordMapper
  implements IBillingCycleRecordMapper
{
  fromRecordToDto(record: Partial<BillingCycleRecord>): Partial<BillingCycleDto> {
    return {
      id: record.id,
      tenantMembershipId: record.tenant_membership_id,
      paymentMethodId: record.payment_method_id,
      status: record.status,
      periodStart: record.period_start,
      periodEnd: record.period_end,
      invoiceGateway: record.invoice_gateway,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: Partial<BillingCycleDto>): Partial<BillingCycleRecord> {
    return {
      id: dto.id,
      tenant_membership_id: dto.tenantMembershipId,
      payment_method_id: dto.paymentMethodId,
      period_start: dto.periodStart,
      period_end: dto.periodEnd,
      status: dto.status,
      invoice_gateway: dto.invoiceGateway,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }
}
