import { BaseR<PERSON>ordMapper, IR<PERSON>ordMapper } from "@cbidigital/aqua-ddd";
import { BillingCycleRecordMapper } from "./billing-cycle.record-mapper";
import { TenantMembershipRecord } from "@features/infra/databases/records";
import { MembershipRecordMapper } from "@features/infra/databases/record-mappers/membership.record-mapper";
import { MembershipCycleRecordMapper } from "@features/infra/databases/record-mappers/membership-cyclle.record-mapper";
import { TenantMembershipExternalMappingRecordMapper } from "@features/infra/databases/record-mappers/tenant-membership-external.record-mapper";
import {
  MembershipDto,
  BillingCycleDto,
  MembershipCycleDto,
  TenantMembershipDto,
  TenantMembershipExternalMappingDto,
} from "@features/domain";

export type ITenantMembershipRecordMapper = IRecordMapper<
  TenantMembershipDto,
  TenantMembershipRecord
>;

export class TenantMembershipRecordMapper
  extends BaseRecordMapper
  implements ITenantMembershipRecordMapper
{
  private membershipRecordMapper = new MembershipRecordMapper();
  private billingCycleRecordMapper = new BillingCycleRecordMapper();
  private membershipCycleRecordMapper = new MembershipCycleRecordMapper();
  private membershipExternalMappingRecordMapper = new TenantMembershipExternalMappingRecordMapper();

  fromRecordToDto(record: Partial<TenantMembershipRecord>): Partial<TenantMembershipDto> {
    const billingCycles = record.billing_cycles
      ? this.billingCycleRecordMapper.fromRecordsToDtos(record.billing_cycles)
      : null;
    const membership = record.membership
      ? this.membershipRecordMapper.fromRecordToDto(record.membership)
      : null;
    const membershipCycle = record.membership_cycle
      ? this.membershipCycleRecordMapper.fromRecordToDto(record.membership_cycle)
      : null;

    const externalMappings = record.external_mappings
      ? record.external_mappings.map((mappingRecord) =>
          this.membershipExternalMappingRecordMapper.fromRecordToDto(mappingRecord),
        )
      : undefined;

    return {
      id: record.id,
      tenantId: record.tenant_id,
      membershipId: record.membership_id,
      paymentMethodId: record.payment_method_id,
      membershipCycleId: record.membership_cycle_id,
      status: record.status,
      trialEnd: record.trial_end,
      createdAt: record.created_at,
      createdBy: record.created_by,
      updatedAt: record.updated_at,
      updatedBy: record.updated_by,
      membership: membership as MembershipDto,
      membershipCycle: membershipCycle as MembershipCycleDto,
      billingCycles: billingCycles as BillingCycleDto[],
      externalTenantMembershipMappings: externalMappings as TenantMembershipExternalMappingDto[],
    };
  }

  fromDtoToRecord(dto: Partial<TenantMembershipDto>): Partial<TenantMembershipRecord> {
    return {
      id: dto.id,
      tenant_id: dto.tenantId,
      membership_id: dto.membershipId,
      payment_method_id: dto.paymentMethodId,
      membership_cycle_id: dto.membershipCycleId,
      status: dto.status,
      trial_end: dto.trialEnd,
      created_at: dto.createdAt,
      created_by: dto.createdBy,
      updated_at: dto.updatedAt,
      updated_by: dto.updatedBy,
    };
  }
}
